#!/usr/bin/env python3
"""
测试历史记录清理功能
"""

import json
import os
from datetime import datetime, timedelta
from auth import User<PERSON>ana<PERSON>

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 备份原始数据
    if os.path.exists('users.json'):
        os.rename('users.json', 'users.json.original')
    
    # 创建测试用户数据
    test_users = {
        "test_user1": {
            "username": "test_user1",
            "password": "hashed_password",
            "email": "<EMAIL>",
            "points": 100,
            "created_at": "2025-07-01T10:00:00",
            "last_login": None,
            "is_admin": False,
            "total_generated": 80,
            "generation_history": []
        },
        "test_user2": {
            "username": "test_user2", 
            "password": "hashed_password",
            "email": "<EMAIL>",
            "points": 50,
            "created_at": "2025-07-10T10:00:00",
            "last_login": None,
            "is_admin": False,
            "total_generated": 120,
            "generation_history": []
        }
    }
    
    # 为test_user1创建80条历史记录（超过50条限制）
    base_time = datetime.now()
    for i in range(80):
        # 创建不同时间的记录，有些超过30天
        days_ago = i // 3  # 每3条记录增加1天
        record_time = base_time - timedelta(days=days_ago)
        
        record = {
            "type": "image",
            "prompt": f"test prompt {i}",
            "timestamp": record_time.isoformat(),
            "success": True
        }
        test_users["test_user1"]["generation_history"].append(record)
    
    # 为test_user2创建120条历史记录
    for i in range(120):
        days_ago = i // 4  # 每4条记录增加1天
        record_time = base_time - timedelta(days=days_ago)
        
        record = {
            "type": "image",
            "prompt": f"another test prompt {i}",
            "timestamp": record_time.isoformat(),
            "success": True
        }
        test_users["test_user2"]["generation_history"].append(record)
    
    # 保存测试数据
    with open('users.json', 'w', encoding='utf-8') as f:
        json.dump(test_users, f, ensure_ascii=False, indent=2)
    
    print(f"测试数据创建完成:")
    print(f"  - test_user1: {len(test_users['test_user1']['generation_history'])} 条记录")
    print(f"  - test_user2: {len(test_users['test_user2']['generation_history'])} 条记录")

def test_cleanup():
    """测试清理功能"""
    print("\n开始测试清理功能...")
    
    # 创建用户管理器
    user_manager = UserManager('users.json')
    
    # 显示清理前的统计
    print("\n清理前统计:")
    for username, user in user_manager.users.items():
        history_count = len(user.get('generation_history', []))
        print(f"  - {username}: {history_count} 条记录")
    
    # 执行清理（模拟）
    print("\n执行模拟清理...")
    stats = user_manager.cleanup_all_users_history(max_records=50, days_to_keep=30)
    
    print(f"模拟清理结果:")
    print(f"  - 受影响用户数: {stats['cleaned_users']}")
    print(f"  - 清理前总记录数: {stats['total_records_before']}")
    print(f"  - 清理后总记录数: {stats['total_records_after']}")
    print(f"  - 删除记录数: {stats['records_removed']}")
    
    if stats['records_removed'] > 0:
        reduction_percent = (stats['records_removed'] / stats['total_records_before']) * 100
        print(f"  - 数据减少: {reduction_percent:.1f}%")
    
    # 显示清理后的统计
    print("\n清理后统计:")
    for username, user in user_manager.users.items():
        history_count = len(user.get('generation_history', []))
        print(f"  - {username}: {history_count} 条记录")

def test_add_record_with_limit():
    """测试添加记录时的自动限制功能"""
    print("\n测试添加记录时的自动限制...")
    
    user_manager = UserManager('users.json')
    
    # 为test_user1添加新记录，应该触发自动清理
    print("为test_user1添加新记录...")
    user_manager.add_generation_record("test_user1", "image", "new test prompt", True, max_records=50)
    
    # 检查记录数量
    user1_history = user_manager.users["test_user1"]["generation_history"]
    print(f"添加后test_user1记录数: {len(user1_history)}")
    
    if len(user1_history) <= 50:
        print("✓ 自动限制功能正常工作")
    else:
        print("✗ 自动限制功能未生效")

def restore_original_data():
    """恢复原始数据"""
    print("\n恢复原始数据...")
    
    if os.path.exists('users.json.original'):
        if os.path.exists('users.json'):
            os.remove('users.json')
        os.rename('users.json.original', 'users.json')
        print("原始数据已恢复")
    else:
        print("未找到原始数据备份")

def main():
    """主测试函数"""
    print("=== 历史记录清理功能测试 ===")
    
    try:
        # 创建测试数据
        create_test_data()
        
        # 测试清理功能
        test_cleanup()
        
        # 测试自动限制功能
        test_add_record_with_limit()
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复原始数据
        restore_original_data()

if __name__ == '__main__':
    main()
