# 用户历史记录管理优化

## 问题描述

原系统中 `user.json` 文件的 `generation_history` 字段会无限制地记录用户的生成历史，随着用户使用次数增加，数据文件会变得越来越大，导致：

1. 文件读写性能下降
2. 存储空间浪费
3. 备份和传输困难
4. 系统响应速度变慢

## 解决方案

### 1. 自动限制历史记录数量

**修改文件**: `auth.py`

- 将默认最大记录数从 100 条减少到 50 条
- 添加可配置的记录数量限制参数
- 每次添加记录时自动清理超出限制的旧记录

```python
def add_generation_record(self, username, generation_type, prompt, success=True, max_records=50):
    # 只保留最近的指定条数记录，减少存储空间
    if len(user['generation_history']) > max_records:
        user['generation_history'] = user['generation_history'][-max_records:]
```

### 2. 基于时间的自动清理

**新增功能**: 自动清理超过指定天数的历史记录

- 默认保留 30 天内的记录
- 自动过滤掉过期的记录
- 保护数据完整性，格式错误的记录会被保留

```python
def _cleanup_old_records(self, user, days_to_keep=30):
    # 清理超过指定天数的记录
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    # 过滤掉过期的记录
```

### 3. 批量清理工具

**新增文件**: `cleanup_history.py`

提供命令行工具用于批量清理所有用户的历史记录：

```bash
# 模拟运行，查看清理效果
python cleanup_history.py --dry-run

# 设置每用户最大30条记录，保留15天
python cleanup_history.py --max-records 30 --days-to-keep 15

# 使用自定义数据文件
python cleanup_history.py --data-file custom_users.json
```

### 4. 配置文件管理

**新增文件**: `cleanup_config.json`

提供灵活的配置选项：

- 不同用户等级的差异化策略
- 自动清理开关和间隔设置
- 备份和日志配置

```json
{
  "user_tier_settings": {
    "admin": {"max_records": 200, "days_to_keep": 90},
    "premium": {"max_records": 100, "days_to_keep": 60},
    "regular": {"max_records": 50, "days_to_keep": 30}
  }
}
```

### 5. 管理员界面

**修改文件**: `templates/admin.html`, `app.py`

在管理员面板中添加"历史记录管理"标签页，提供：

- **统计信息显示**：
  - 总用户数和有历史记录的用户数
  - 总记录数和平均记录数
  - 最近7天/30天的记录统计
  - 单用户最大记录数

- **清理操作**：
  - 可配置的清理参数（最大记录数、保留天数）
  - 模拟运行功能，预览清理效果
  - 实际清理操作，显示详细统计结果

- **API接口**：
  - `/admin/history_statistics` - 获取历史记录统计
  - `/admin/cleanup_history` - 执行历史记录清理

## 使用方法

### 1. 自动清理（推荐）

系统会在每次添加新记录时自动清理：
- 超过50条的历史记录
- 超过30天的过期记录

### 2. 手动清理

#### 通过管理员界面：
1. 登录管理员账户
2. 进入"历史记录管理"标签页
3. 查看当前统计信息
4. 设置清理参数
5. 先执行"预览清理效果"
6. 确认无误后执行实际清理

#### 通过命令行：
```bash
# 查看帮助
python cleanup_history.py --help

# 预览清理效果
python cleanup_history.py --dry-run

# 执行清理
python cleanup_history.py --max-records 50 --days-to-keep 30
```

### 3. 定期自动清理

可以通过系统的任务计划程序设置定期清理：

**Windows (任务计划程序)**:
```
程序: python
参数: cleanup_history.py --max-records 50 --days-to-keep 30
工作目录: E:\GCVC\fufei
触发器: 每天凌晨2点
```

**Linux (crontab)**:
```bash
# 每天凌晨2点执行清理
0 2 * * * cd /path/to/fufei && python cleanup_history.py --max-records 50 --days-to-keep 30
```

## 配置建议

### 用户等级配置

- **管理员**: 200条记录，保留90天
- **高级用户**: 100条记录，保留60天  
- **普通用户**: 50条记录，保留30天

### 清理频率

- **高频用户系统**: 每天清理一次
- **中频用户系统**: 每周清理一次
- **低频用户系统**: 每月清理一次

## 安全保障

1. **备份机制**: 每次保存用户数据时自动创建备份文件
2. **模拟运行**: 提供预览功能，避免误删重要数据
3. **错误处理**: 时间戳格式错误的记录会被保留，避免数据丢失
4. **操作日志**: 管理员操作会被记录到系统日志

## 性能提升

通过实施这些优化措施，预期可以获得：

- **文件大小减少**: 60-80%（取决于用户活跃度）
- **读写性能提升**: 30-50%
- **内存使用减少**: 40-60%
- **备份速度提升**: 50-70%

## 注意事项

1. 首次执行清理时建议先使用模拟运行模式
2. 重要用户的历史记录可以考虑单独备份
3. 清理操作不可撤销，请谨慎操作
4. 建议在系统使用低峰期执行清理操作
