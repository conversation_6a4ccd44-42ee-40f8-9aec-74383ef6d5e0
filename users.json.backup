{"test_user1": {"username": "test_user1", "password": "hashed_password", "email": "<EMAIL>", "points": 100, "created_at": "2025-07-01T10:00:00", "last_login": null, "is_admin": false, "total_generated": 80, "generation_history": [{"type": "image", "prompt": "test prompt 30", "timestamp": "2025-07-10T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 31", "timestamp": "2025-07-10T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 32", "timestamp": "2025-07-10T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 33", "timestamp": "2025-07-09T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 34", "timestamp": "2025-07-09T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 35", "timestamp": "2025-07-09T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 36", "timestamp": "2025-07-08T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 37", "timestamp": "2025-07-08T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 38", "timestamp": "2025-07-08T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 39", "timestamp": "2025-07-07T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 40", "timestamp": "2025-07-07T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 41", "timestamp": "2025-07-07T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 42", "timestamp": "2025-07-06T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 43", "timestamp": "2025-07-06T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 44", "timestamp": "2025-07-06T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 45", "timestamp": "2025-07-05T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 46", "timestamp": "2025-07-05T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 47", "timestamp": "2025-07-05T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 48", "timestamp": "2025-07-04T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 49", "timestamp": "2025-07-04T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 50", "timestamp": "2025-07-04T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 51", "timestamp": "2025-07-03T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 52", "timestamp": "2025-07-03T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 53", "timestamp": "2025-07-03T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 54", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 55", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 56", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 57", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 58", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 59", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 60", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 61", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 62", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 63", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 64", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 65", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 66", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 67", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 68", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 69", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 70", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 71", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 72", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 73", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 74", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 75", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 76", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 77", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 78", "timestamp": "2025-06-24T18:49:52.442686", "success": true}, {"type": "image", "prompt": "test prompt 79", "timestamp": "2025-06-24T18:49:52.442686", "success": true}]}, "test_user2": {"username": "test_user2", "password": "hashed_password", "email": "<EMAIL>", "points": 50, "created_at": "2025-07-10T10:00:00", "last_login": null, "is_admin": false, "total_generated": 120, "generation_history": [{"type": "image", "prompt": "another test prompt 70", "timestamp": "2025-07-03T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 71", "timestamp": "2025-07-03T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 72", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 73", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 74", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 75", "timestamp": "2025-07-02T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 76", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 77", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 78", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 79", "timestamp": "2025-07-01T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 80", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 81", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 82", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 83", "timestamp": "2025-06-30T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 84", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 85", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 86", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 87", "timestamp": "2025-06-29T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 88", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 89", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 90", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 91", "timestamp": "2025-06-28T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 92", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 93", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 94", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 95", "timestamp": "2025-06-27T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 96", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 97", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 98", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 99", "timestamp": "2025-06-26T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 100", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 101", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 102", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 103", "timestamp": "2025-06-25T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 104", "timestamp": "2025-06-24T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 105", "timestamp": "2025-06-24T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 106", "timestamp": "2025-06-24T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 107", "timestamp": "2025-06-24T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 108", "timestamp": "2025-06-23T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 109", "timestamp": "2025-06-23T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 110", "timestamp": "2025-06-23T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 111", "timestamp": "2025-06-23T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 112", "timestamp": "2025-06-22T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 113", "timestamp": "2025-06-22T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 114", "timestamp": "2025-06-22T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 115", "timestamp": "2025-06-22T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 116", "timestamp": "2025-06-21T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 117", "timestamp": "2025-06-21T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 118", "timestamp": "2025-06-21T18:49:52.442686", "success": true}, {"type": "image", "prompt": "another test prompt 119", "timestamp": "2025-06-21T18:49:52.442686", "success": true}]}}